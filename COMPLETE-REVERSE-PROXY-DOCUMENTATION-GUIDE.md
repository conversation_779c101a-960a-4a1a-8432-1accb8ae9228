# Complete Reverse Proxy Setup Documentation & Migration Guide

## Overview
This guide provides step-by-step instructions for documenting your current reverse proxy setup and recreating it on new servers. Your current setup follows this flow:

```
C<PERSON> Browser → Cloudflare → NGINX Reverse Proxy (*************) → Backend Server (***********)
```

## Phase 1: Document Current Setup

### Step 1: Prepare Documentation Scripts

**What to do:** Download and prepare the audit scripts
**When to do:** Before accessing your servers
**Where to go:** Your local computer

1. Download the two scripts I created:
   - `reverse-proxy-audit-script.sh` (for *************)
   - `backend-server-audit-script.sh` (for ***********)

2. Make them executable:
   ```bash
   chmod +x reverse-proxy-audit-script.sh
   chmod +x backend-server-audit-script.sh
   ```

**What to expect:** <PERSON>ripts should be ready to upload to servers

### Step 2: Document Reverse Proxy Server (*************)

**What to do:** Run documentation script on your NGINX reverse proxy server
**When to do:** After preparing scripts
**Where to go:** SSH into *************

1. **Connect to your reverse proxy server:**
   ```bash
   ssh root@*************
   ```

2. **Upload the script:**
   ```bash
   # From your local machine
   scp reverse-proxy-audit-script.sh root@*************:/tmp/
   ```

3. **Run the documentation script:**
   ```bash
   # On the server
   cd /tmp
   chmod +x reverse-proxy-audit-script.sh
   ./reverse-proxy-audit-script.sh
   ```

4. **Download the generated documentation:**
   ```bash
   # From your local machine
   scp root@*************:/tmp/reverse-proxy-docs/reverse-proxy-configuration.md ./
   ```

**What to expect:** 
- Script will run for 2-5 minutes
- Will generate a comprehensive .md file with all NGINX configurations
- File will be saved in `/tmp/reverse-proxy-docs/`

**If issues found:**
- **Permission denied:** Run `chmod +x` on the script
- **Command not found:** Install missing packages: `apt update && apt install curl net-tools`
- **Network issues:** Check if server can reach internet: `ping google.com`

### Step 3: Document Backend Server (***********)

**What to do:** Run documentation script on your backend server
**When to do:** After completing reverse proxy documentation
**Where to go:** SSH into ***********

1. **Connect to your backend server:**
   ```bash
   ssh root@***********
   ```

2. **Upload the script:**
   ```bash
   # From your local machine
   scp backend-server-audit-script.sh root@***********:/tmp/
   ```

3. **Run the documentation script:**
   ```bash
   # On the server
   cd /tmp
   chmod +x backend-server-audit-script.sh
   ./backend-server-audit-script.sh
   ```

4. **Download the generated documentation:**
   ```bash
   # From your local machine
   scp root@***********:/tmp/backend-server-docs/backend-server-configuration.md ./
   ```

**What to expect:**
- Script will run for 3-7 minutes
- Will generate documentation for FastPanel, Apache/NGINX, MySQL, PHP
- File will be saved in `/tmp/backend-server-docs/`

**If issues found:**
- **MySQL access denied:** Script will note this and continue
- **FastPanel not found:** Script will document what it can find
- **Missing services:** Script will note missing components

### Step 4: Document Cloudflare Configuration

**What to do:** Manually document your Cloudflare settings
**When to do:** After server documentation is complete
**Where to go:** Cloudflare Dashboard

1. **Login to Cloudflare:**
   - Go to https://dash.cloudflare.com
   - Login with your account

2. **Select your domain (streamdb.online):**
   - Click on your domain from the dashboard

3. **Document DNS Settings:**
   - Go to DNS tab
   - Take screenshots of all DNS records
   - Note down:
     - A records pointing to *************
     - CNAME records
     - MX records
     - Any other custom records

4. **Document SSL/TLS Settings:**
   - Go to SSL/TLS tab
   - Note the SSL/TLS encryption mode (Full, Flexible, etc.)
   - Document any custom certificates
   - Note Edge Certificates settings

5. **Document Security Settings:**
   - Go to Security tab
   - Note Security Level setting
   - Document any WAF rules
   - Note Bot Fight Mode settings

6. **Document Speed Settings:**
   - Go to Speed tab
   - Note Auto Minify settings
   - Document Brotli compression
   - Note any Page Rules

7. **Document Page Rules:**
   - Go to Rules → Page Rules
   - Document all page rules and their settings

8. **Document Firewall Rules:**
   - Go to Security → WAF
   - Document any custom firewall rules

**What to expect:**
- Complete overview of all Cloudflare configurations
- Screenshots and notes for each section

**If issues found:**
- **Can't access dashboard:** Check login credentials
- **Domain not showing:** Verify domain ownership
- **Missing settings:** Some features may be Pro/Business only

## Phase 2: Create Migration Documentation

### Step 5: Analyze Generated Documentation

**What to do:** Review all generated documentation files
**When to do:** After all documentation is collected
**Where to go:** Your local computer

1. **Review files:**
   - `reverse-proxy-configuration.md`
   - `backend-server-configuration.md`
   - Cloudflare screenshots/notes

2. **Identify key configurations:**
   - NGINX virtual host configurations
   - SSL certificate locations and settings
   - Database connection details
   - FastPanel configurations
   - Domain DNS settings

**What to expect:** Clear understanding of your current setup

### Step 6: Create Server Requirements Document

**What to do:** Document exact server specifications needed
**When to do:** After analyzing current setup
**Where to go:** Create new documentation file

Based on your current setup, document:

1. **Reverse Proxy Server Requirements:**
   - OS version and architecture
   - Minimum RAM and CPU
   - Required packages (NGINX version, modules)
   - Network requirements

2. **Backend Server Requirements:**
   - OS version and architecture
   - Minimum RAM and CPU
   - Required packages (Apache/NGINX, PHP, MySQL, FastPanel)
   - Storage requirements

**What to expect:** Clear server specifications for new hosting provider

## Phase 3: Migration Planning

### Step 7: Create Migration Checklist

**What to do:** Create detailed migration steps
**When to do:** After requirements are documented
**Where to go:** Create comprehensive checklist

The migration checklist should include:

1. **Pre-Migration:**
   - [ ] Order new servers with correct specifications
   - [ ] Backup current website files and database
   - [ ] Document current DNS TTL settings
   - [ ] Prepare SSL certificates for transfer

2. **Server Setup:**
   - [ ] Install base OS on both servers
   - [ ] Configure network settings
   - [ ] Install required packages
   - [ ] Configure firewalls

3. **Reverse Proxy Setup:**
   - [ ] Install NGINX with same modules
   - [ ] Copy NGINX configurations
   - [ ] Install SSL certificates
   - [ ] Test proxy functionality

4. **Backend Server Setup:**
   - [ ] Install FastPanel
   - [ ] Install and configure MySQL
   - [ ] Install and configure PHP
   - [ ] Install and configure web server
   - [ ] Restore website files
   - [ ] Restore database

5. **DNS Migration:**
   - [ ] Lower DNS TTL to 300 seconds
   - [ ] Update A records to new reverse proxy IP
   - [ ] Monitor traffic switch
   - [ ] Verify all functionality

6. **Post-Migration:**
   - [ ] Monitor logs for errors
   - [ ] Test all website functionality
   - [ ] Update monitoring systems
   - [ ] Document new server details

**What to expect:** Complete roadmap for migration

### Step 8: Create Rollback Plan

**What to do:** Plan for potential migration issues
**When to do:** Before starting migration
**Where to go:** Document rollback procedures

1. **Immediate Rollback:**
   - Change DNS back to original servers
   - Keep original servers running during migration

2. **Partial Rollback:**
   - Procedures for rolling back individual components
   - Database restoration procedures

3. **Emergency Contacts:**
   - Hosting provider support
   - Domain registrar support
   - Cloudflare support

**What to expect:** Safety net for migration process

## Phase 4: Testing and Validation

### Step 9: Create Testing Procedures

**What to do:** Define comprehensive testing steps
**When to do:** Before migration starts
**Where to go:** Create testing documentation

1. **Functionality Tests:**
   - Website loading and navigation
   - Database connectivity
   - User authentication
   - File uploads/downloads
   - Search functionality

2. **Performance Tests:**
   - Page load times
   - Database query performance
   - SSL certificate validation
   - CDN functionality

3. **Security Tests:**
   - SSL/TLS configuration
   - Firewall rules
   - Access controls
   - Backup procedures

**What to expect:** Comprehensive testing framework

### Step 10: Monitor and Optimize

**What to do:** Set up monitoring for new setup
**When to do:** After successful migration
**Where to go:** Configure monitoring tools

1. **Server Monitoring:**
   - CPU and memory usage
   - Disk space
   - Network connectivity
   - Service uptime

2. **Application Monitoring:**
   - Website uptime
   - Response times
   - Error rates
   - Database performance

3. **Security Monitoring:**
   - Failed login attempts
   - Unusual traffic patterns
   - SSL certificate expiration
   - Security updates

**What to expect:** Proactive monitoring of new setup

## Important Notes

### Security Considerations
- Always use strong passwords and SSH keys
- Keep all software updated
- Regular security audits
- Backup encryption

### Performance Optimization
- Monitor resource usage
- Optimize database queries
- Configure caching properly
- Regular performance reviews

### Maintenance Schedule
- Weekly security updates
- Monthly performance reviews
- Quarterly full backups
- Annual security audits

## Emergency Procedures

### If Migration Fails
1. Immediately revert DNS to original servers
2. Check error logs on new servers
3. Contact hosting provider support
4. Document issues for troubleshooting

### If Original Servers Fail During Migration
1. Accelerate migration timeline
2. Use backup files to restore on new servers
3. Update DNS immediately
4. Monitor closely for issues

### If DNS Issues Occur
1. Contact domain registrar
2. Use alternative DNS providers if needed
3. Communicate with users about temporary issues
4. Document resolution steps

This guide provides a complete framework for documenting and migrating your reverse proxy setup. Each step includes specific actions, expected outcomes, and troubleshooting procedures to ensure a successful migration.

## Quick Start Summary

### For Immediate Documentation (Do This First):

1. **Download Scripts:** Get the two audit scripts from this repository
2. **Run on Reverse Proxy Server (*************):**
   ```bash
   scp reverse-proxy-audit-script.sh root@*************:/tmp/
   ssh root@*************
   cd /tmp && chmod +x reverse-proxy-audit-script.sh && ./reverse-proxy-audit-script.sh
   ```
3. **Run on Backend Server (***********):**
   ```bash
   scp backend-server-audit-script.sh root@***********:/tmp/
   ssh root@***********
   cd /tmp && chmod +x backend-server-audit-script.sh && ./backend-server-audit-script.sh
   ```
4. **Download Results:**
   ```bash
   scp root@*************:/tmp/reverse-proxy-docs/reverse-proxy-configuration.md ./
   scp root@***********:/tmp/backend-server-docs/backend-server-configuration.md ./
   ```

### Next Steps:
- Review the generated documentation files
- Document your Cloudflare settings manually
- Use this guide to plan your migration
- Follow the detailed steps for a complete migration

## Support and Troubleshooting

If you encounter any issues:
1. Check the troubleshooting sections in each step
2. Verify network connectivity between servers
3. Ensure all required packages are installed
4. Contact your hosting provider if server access issues occur

Remember: Always test changes in a staging environment first, and keep backups of all configurations before making changes.
