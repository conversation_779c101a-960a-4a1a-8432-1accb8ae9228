# Complete Reverse Proxy Configuration Analysis & Migration Guide

## Overview

This guide will help you analyze your generated documentation files and recreate your exact reverse proxy setup on new servers. Your current setup follows this flow:

```
Client Browser → Cloudflare → NGINX Reverse Proxy (*************) → Backend Server (***********)
```

## Prerequisites

Before starting, ensure you have these files from running the audit scripts:
- `reverse-proxy-configuration.md` (from *************)
- `backend-server-configuration.md` (from ***********)
- `cloudflare-configuration.md` (manually documented)

---

# PHASE 1: ANALYZE YOUR CURRENT CONFIGURATION

## Step 1: Extract Reverse Proxy Information

**What to do:** Analyze your reverse proxy documentation
**When to do:** Before ordering new servers
**Where to go:** Open `reverse-proxy-configuration.md`

### 1.1: Server Specifications
**What to look for:**
- Find the "Server Details" section
- Note down the OS version (e.g., Ubuntu 20.04, CentOS 8)
- Record CPU information (cores, architecture)
- Note memory size (RAM)
- Record disk space and usage

**What to expect:** Information like:
```
OS: Ubuntu 20.04.3 LTS
CPU: 2 cores, x86_64
Memory: 4GB RAM
Disk: 80GB SSD
```

**Create this table:**
| Component | Current Value | Required for New Server |
|-----------|---------------|------------------------|
| OS | [FILL FROM DOC] | Same or newer version |
| CPU | [FILL FROM DOC] | Same or better |
| RAM | [FILL FROM DOC] | Same or more |
| Disk | [FILL FROM DOC] | Same or more |

### 1.2: NGINX Configuration Analysis
**What to look for:**
- Find "NGINX Version and Modules" section
- Note the exact NGINX version
- Record all compiled modules (look for --with-* flags)
- Find "NGINX Main Configuration" section
- Copy the entire nginx.conf content to a separate file

**What to expect:** Information like:
```
nginx version: nginx/1.18.0
built with OpenSSL 1.1.1f
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_v2_module
```

**Action required:**
1. Create a file called `nginx-main-config.conf`
2. Copy the entire NGINX main configuration from the documentation
3. Note any custom modules or special compilation flags

### 1.3: Virtual Host Configuration
**What to look for:**
- Find "NGINX Site Configurations" section
- Look for files in `/etc/nginx/sites-enabled/`
- Copy each virtual host configuration

**What to expect:** One or more server blocks like:
```nginx
server {
    listen 80;
    listen 443 ssl;
    server_name streamdb.online www.streamdb.online;
    
    location / {
        proxy_pass http://***********;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

**Action required:**
1. Create separate files for each virtual host
2. Name them descriptively (e.g., `streamdb-online-vhost.conf`)
3. Note the backend server IP (***********) - this will change in migration

### 1.4: SSL Certificate Information
**What to look for:**
- Find "SSL Certificates" section
- Note certificate file locations
- Check if using Let's Encrypt or custom certificates
- Record certificate domains and expiration dates

**What to expect:**
```
/etc/letsencrypt/live/streamdb.online/fullchain.pem
/etc/letsencrypt/live/streamdb.online/privkey.pem
```

**Action required:**
1. Note certificate type (Let's Encrypt, custom, etc.)
2. Record all domains covered by certificates
3. Plan certificate migration strategy

### 1.5: Firewall Configuration
**What to look for:**
- Find "Firewall Configuration" section
- Note UFW or iptables rules
- Record which ports are open

**What to expect:**
```
Status: active
To                         Action      From
--                         ------      ----
22/tcp                     ALLOW       Anywhere
80/tcp                     ALLOW       Anywhere
443/tcp                    ALLOW       Anywhere
```

**Action required:**
1. List all open ports and their purposes
2. Note any IP-specific rules
3. Record firewall software used (UFW/iptables)

## Step 2: Extract Backend Server Information

**What to do:** Analyze your backend server documentation
**When to do:** After completing reverse proxy analysis
**Where to go:** Open `backend-server-configuration.md`

### 2.1: Server Specifications
**What to look for:**
- Same as reverse proxy: OS, CPU, RAM, disk
- Note any differences in specifications

**Action required:**
1. Fill in the same table format as reverse proxy
2. Compare specifications between servers
3. Plan for adequate resources on new backend server

### 2.2: FastPanel Configuration
**What to look for:**
- Find "FastPanel Control Panel" section
- Note FastPanel version and status
- Record configuration file locations
- Check which services FastPanel manages

**What to expect:**
```
FastPanel service: active (running)
Configuration: /etc/fastpanel/
Version: [specific version number]
```

**Action required:**
1. Note FastPanel version for reinstallation
2. Record any custom FastPanel configurations
3. List services managed by FastPanel

### 2.3: Web Server Configuration
**What to look for:**
- Find "Web Server Configuration" section
- Determine if using Apache or NGINX
- Note version and modules
- Copy main configuration files
- Record virtual host configurations

**What to expect for Apache:**
```
Apache/2.4.41 (Ubuntu)
Modules: mod_rewrite, mod_ssl, mod_php
Virtual hosts in /etc/apache2/sites-enabled/
```

**Action required:**
1. Create file `backend-webserver-config.conf`
2. Copy main web server configuration
3. Copy all virtual host configurations
4. Note document root locations (usually `/var/www/`)

### 2.4: MySQL Database Configuration
**What to look for:**
- Find "MySQL Database Configuration" section
- Note MySQL/MariaDB version
- Record configuration file locations
- Note any custom settings

**What to expect:**
```
mysql Ver 8.0.25-0ubuntu0.20.04.1
Configuration: /etc/mysql/my.cnf
```

**Action required:**
1. Copy MySQL configuration files
2. Note database version for compatibility
3. Record any performance tuning settings
4. Plan database migration strategy

### 2.5: PHP Configuration
**What to look for:**
- Find "PHP Configuration" section
- Note PHP version
- Record php.ini location and settings
- Note installed extensions

**What to expect:**
```
PHP 7.4.3 (cli)
Configuration file: /etc/php/7.4/apache2/php.ini
Extensions: mysqli, curl, gd, mbstring
```

**Action required:**
1. Copy PHP configuration file
2. List all required PHP extensions
3. Note any custom PHP settings
4. Record PHP version for new server

### 2.6: Website Files Location
**What to look for:**
- Find "Website Configuration" section
- Note document root locations
- Record file permissions and ownership
- Check for any special directories

**What to expect:**
```
/var/www/streamdb.online/
/home/<USER>/public_html/
```

**Action required:**
1. Note exact website file locations
2. Record file ownership (user:group)
3. Note any special permissions
4. Plan file transfer method

## Step 3: Extract Cloudflare Configuration

**What to do:** Analyze your Cloudflare documentation
**When to do:** After server analysis is complete
**Where to go:** Open your completed `cloudflare-configuration.md`

### 3.1: DNS Records Analysis
**What to look for:**
- All A records pointing to *************
- CNAME records
- MX records for email
- Any other custom records

**Action required:**
1. Create a DNS migration plan
2. Note which records need IP updates
3. Record current TTL values
4. Plan TTL reduction before migration

### 3.2: SSL/TLS Settings
**What to look for:**
- SSL/TLS encryption mode (Full, Flexible, etc.)
- HSTS settings
- Certificate settings

**Action required:**
1. Note exact SSL mode for new setup
2. Record any custom SSL settings
3. Plan SSL certificate coordination

### 3.3: Security and Performance Settings
**What to look for:**
- Security level settings
- Firewall rules
- Page rules
- Caching settings

**Action required:**
1. Document all custom rules
2. Note performance optimizations
3. Plan rule updates for new IPs

---

# PHASE 2: PLAN YOUR MIGRATION

## Step 4: Create Migration Plan

**What to do:** Organize all extracted information into a deployment plan
**When to do:** After completing all analysis
**Where to go:** Create a new document called `migration-deployment-plan.md`

### 4.1: Server Requirements Summary
Create this table based on your analysis:

| Server Type | OS Required | CPU | RAM | Disk | Special Requirements |
|-------------|-------------|-----|-----|------|---------------------|
| Reverse Proxy | [FROM ANALYSIS] | [FROM ANALYSIS] | [FROM ANALYSIS] | [FROM ANALYSIS] | NGINX + SSL |
| Backend | [FROM ANALYSIS] | [FROM ANALYSIS] | [FROM ANALYSIS] | [FROM ANALYSIS] | FastPanel + MySQL + PHP |

### 4.2: Software Requirements List
Based on your analysis, create this checklist:

**Reverse Proxy Server:**
- [ ] NGINX version: [SPECIFIC VERSION]
- [ ] SSL modules: [LIST FROM ANALYSIS]
- [ ] Additional modules: [LIST FROM ANALYSIS]
- [ ] Firewall software: [UFW/iptables]

**Backend Server:**
- [ ] FastPanel version: [SPECIFIC VERSION]
- [ ] Web server: [Apache/NGINX + VERSION]
- [ ] MySQL version: [SPECIFIC VERSION]
- [ ] PHP version: [SPECIFIC VERSION]
- [ ] PHP extensions: [LIST FROM ANALYSIS]

### 4.3: Configuration Files Checklist
Ensure you have extracted these files:

**From Reverse Proxy Analysis:**
- [ ] `nginx-main-config.conf`
- [ ] `streamdb-online-vhost.conf`
- [ ] SSL certificate information
- [ ] Firewall rules list

**From Backend Analysis:**
- [ ] `backend-webserver-config.conf`
- [ ] MySQL configuration files
- [ ] PHP configuration file
- [ ] FastPanel settings (if any)

### 4.4: Data Migration Plan
Plan these data transfers:

- [ ] Website files from `/var/www/` (or noted location)
- [ ] Database dump from MySQL
- [ ] SSL certificates (if custom)
- [ ] Any custom scripts or cron jobs

---

# PHASE 3: ORDER AND PREPARE NEW SERVERS

## Step 5: Order New Servers

**What to do:** Order servers matching your requirements
**When to do:** After completing migration plan
**Where to go:** Your chosen hosting provider

### 5.1: Server Specifications
**Order servers with these minimum specifications:**

**New Reverse Proxy Server:**
- OS: [FROM YOUR ANALYSIS]
- CPU: [SAME OR BETTER]
- RAM: [SAME OR MORE]
- Disk: [SAME OR MORE]
- Network: Public IP, unlimited bandwidth preferred

**New Backend Server:**
- OS: [FROM YOUR ANALYSIS]
- CPU: [SAME OR BETTER]
- RAM: [SAME OR MORE]
- Disk: [SAME OR MORE]
- Network: Private network connection to reverse proxy preferred

**What to expect:**
- Server provisioning: 15 minutes to 2 hours
- IP address assignment
- Root access credentials
- Network configuration details

**If issues found:**
- Contact hosting provider support
- Verify server specifications match order
- Test network connectivity

### 5.2: Initial Server Access
**What to do:** Test access to both new servers
**When to do:** Immediately after server provisioning
**Where to go:** SSH to both servers

```bash
# Test reverse proxy server access
ssh root@[NEW-REVERSE-PROXY-IP]

# Test backend server access  
ssh root@[NEW-BACKEND-IP]
```

**What to expect:**
- Successful SSH connection
- Root shell access
- Basic OS installation

**If issues found:**
- Check SSH key configuration
- Verify IP addresses are correct
- Contact hosting provider if connection fails

---

# PHASE 4: CONFIGURE NEW REVERSE PROXY SERVER

## Step 6: Setup New Reverse Proxy Server

**What to do:** Install and configure NGINX on new reverse proxy server
**When to do:** After confirming server access
**Where to go:** SSH to new reverse proxy server

### 6.1: System Updates and Basic Setup
```bash
# Update system packages
apt update && apt upgrade -y

# Install basic tools
apt install -y curl wget nano htop net-tools
```

**What to expect:**
- Package updates to complete (5-15 minutes)
- Basic tools to be available

**If issues found:**
- Check internet connectivity: `ping google.com`
- Verify package repositories: `apt update`

### 6.2: Install NGINX with Required Modules
**Based on your analysis, install NGINX:**

```bash
# Install NGINX (check your analysis for specific version requirements)
apt install -y nginx

# Verify installation
nginx -V
```

**What to expect:**
- NGINX installation to complete
- Version output matching or newer than your analysis

**If issues found:**
- If specific version needed: use official NGINX repository
- If modules missing: compile from source or find appropriate packages

### 6.3: Configure NGINX Main Configuration
**What to do:** Apply your extracted NGINX configuration
**When to do:** After NGINX installation
**Where to go:** Edit `/etc/nginx/nginx.conf`

```bash
# Backup default configuration
cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup

# Edit main configuration
nano /etc/nginx/nginx.conf
```

**Action required:**
1. Replace content with your extracted `nginx-main-config.conf`
2. Update any server-specific settings (worker processes, etc.)
3. Verify configuration: `nginx -t`

**What to expect:**
- Configuration syntax to be valid
- No error messages from `nginx -t`

**If issues found:**
- Check syntax errors line by line
- Verify file paths exist
- Ensure modules are available

### 6.4: Configure Virtual Hosts
**What to do:** Set up your website virtual host
**When to do:** After main NGINX configuration
**Where to go:** Create files in `/etc/nginx/sites-available/`

```bash
# Create virtual host file
nano /etc/nginx/sites-available/streamdb.online
```

**Action required:**
1. Copy content from your extracted `streamdb-online-vhost.conf`
2. **IMPORTANT:** Update backend IP from *********** to your new backend server IP
3. Remove SSL configuration temporarily (will add after certificates)

**Example configuration update:**
```nginx
server {
    listen 80;
    server_name streamdb.online www.streamdb.online;
    
    location / {
        proxy_pass http://[NEW-BACKEND-SERVER-IP];  # Update this IP
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

**Enable the site:**
```bash
# Enable site
ln -s /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-enabled/

# Test configuration
nginx -t

# Reload NGINX
systemctl reload nginx
```

**What to expect:**
- Configuration to pass syntax check
- NGINX to reload without errors

**If issues found:**
- Check virtual host syntax
- Verify backend server IP is correct
- Ensure site is properly enabled

### 6.5: Configure Firewall
**What to do:** Set up firewall rules based on your analysis
**When to do:** After NGINX configuration
**Where to go:** Configure UFW or iptables

**If using UFW (most common):**
```bash
# Install UFW if not present
apt install -y ufw

# Configure basic rules
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp

# Enable firewall
ufw enable
```

**What to expect:**
- Firewall rules to be applied
- SSH access to remain working
- HTTP/HTTPS ports to be open

**If issues found:**
- If SSH access lost: contact hosting provider console access
- Verify rules with: `ufw status verbose`

---

# PHASE 5: CONFIGURE NEW BACKEND SERVER

## Step 7: Setup New Backend Server

**What to do:** Install and configure all backend services
**When to do:** After reverse proxy basic setup
**Where to go:** SSH to new backend server

### 7.1: System Updates and Basic Setup
```bash
# Update system
apt update && apt upgrade -y

# Install basic tools
apt install -y curl wget nano htop net-tools unzip
```

### 7.2: Install FastPanel (if used)
**Based on your analysis, install FastPanel:**

```bash
# Download and install FastPanel (check version from your analysis)
curl -sSL https://fastpanel.direct/install.sh | bash
```

**What to expect:**
- FastPanel installation to complete (10-30 minutes)
- Web interface to become available
- Basic services to be configured

**If issues found:**
- Check FastPanel documentation for your specific version
- Verify system requirements are met
- Contact FastPanel support if needed

### 7.3: Install and Configure MySQL
**What to do:** Install MySQL matching your analysis version
**When to do:** After FastPanel installation

```bash
# Install MySQL (adjust version based on your analysis)
apt install -y mysql-server

# Secure installation
mysql_secure_installation
```

**Action required:**
1. Set strong root password
2. Remove anonymous users
3. Disable remote root login
4. Remove test database

**Apply your MySQL configuration:**
```bash
# Backup default configuration
cp /etc/mysql/my.cnf /etc/mysql/my.cnf.backup

# Edit configuration
nano /etc/mysql/my.cnf
```

**What to do:**
1. Apply settings from your extracted MySQL configuration
2. Restart MySQL: `systemctl restart mysql`
3. Test connection: `mysql -u root -p`

**What to expect:**
- MySQL to start without errors
- Configuration to be applied
- Database connection to work

**If issues found:**
- Check MySQL error logs: `/var/log/mysql/error.log`
- Verify configuration syntax
- Ensure adequate disk space

### 7.4: Install and Configure PHP
**What to do:** Install PHP matching your analysis
**When to do:** After MySQL setup

```bash
# Install PHP and extensions (adjust version based on analysis)
apt install -y php7.4 php7.4-mysql php7.4-curl php7.4-gd php7.4-mbstring php7.4-xml php7.4-zip
```

**Apply PHP configuration:**
```bash
# Backup default PHP configuration
cp /etc/php/7.4/apache2/php.ini /etc/php/7.4/apache2/php.ini.backup

# Edit PHP configuration
nano /etc/php/7.4/apache2/php.ini
```

**Action required:**
1. Apply settings from your extracted PHP configuration
2. Pay attention to memory_limit, upload_max_filesize, etc.
3. Restart web server after changes

**What to expect:**
- PHP to be available with correct version
- All required extensions to be loaded
- Configuration changes to take effect

**If issues found:**
- Check PHP version: `php -v`
- Verify extensions: `php -m`
- Check PHP error logs

### 7.5: Install and Configure Web Server
**What to do:** Install Apache or NGINX based on your analysis
**When to do:** After PHP installation

**If using Apache (most common with FastPanel):**
```bash
# Install Apache
apt install -y apache2

# Enable required modules
a2enmod rewrite
a2enmod ssl
a2enmod headers
```

**Apply web server configuration:**
```bash
# Backup default configuration
cp /etc/apache2/apache2.conf /etc/apache2/apache2.conf.backup

# Edit main configuration
nano /etc/apache2/apache2.conf
```

**Action required:**
1. Apply settings from your extracted web server configuration
2. Configure virtual hosts for streamdb.online
3. Set correct document root

**Create virtual host:**
```bash
# Create site configuration
nano /etc/apache2/sites-available/streamdb.online.conf
```

**Example virtual host:**
```apache
<VirtualHost *:80>
    ServerName streamdb.online
    ServerAlias www.streamdb.online
    DocumentRoot /var/www/streamdb.online
    
    <Directory /var/www/streamdb.online>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/streamdb_error.log
    CustomLog ${APACHE_LOG_DIR}/streamdb_access.log combined
</VirtualHost>
```

**Enable site:**
```bash
# Enable site
a2ensite streamdb.online.conf

# Restart Apache
systemctl restart apache2
```

**What to expect:**
- Web server to start without errors
- Virtual host to be active
- PHP to work with web server

**If issues found:**
- Check Apache error logs: `/var/log/apache2/error.log`
- Verify configuration syntax: `apache2ctl configtest`
- Ensure ports are not conflicting

### 7.6: Configure Backend Firewall
**What to do:** Secure backend server to only allow reverse proxy access
**When to do:** After all services are configured

```bash
# Install UFW
apt install -y ufw

# Configure restrictive rules
ufw default deny incoming
ufw default allow outgoing

# Allow SSH (be careful with this)
ufw allow from [YOUR-IP-ADDRESS] to any port 22

# Allow reverse proxy server access
ufw allow from [NEW-REVERSE-PROXY-IP] to any port 80
ufw allow from [NEW-REVERSE-PROXY-IP] to any port 443

# Enable firewall
ufw enable
```

**What to expect:**
- Backend to be accessible only from reverse proxy
- SSH access to remain from your IP
- All other access to be blocked

**If issues found:**
- If locked out: use hosting provider console
- Verify IP addresses are correct
- Test connectivity from reverse proxy

---

# PHASE 6: MIGRATE DATA AND CONTENT

## Step 8: Transfer Website Files and Database

**What to do:** Move all website data to new backend server
**When to do:** After backend server configuration is complete
**Where to go:** Work between old and new backend servers

### 8.1: Create Database Backup
**What to do:** Export database from old server
**When to do:** Before any data transfer
**Where to go:** SSH to old backend server (***********)

```bash
# Connect to old backend server
ssh root@***********

# Create database backup
mysqldump -u root -p --all-databases > /tmp/full_database_backup.sql

# Or backup specific database if you know the name
mysqldump -u root -p streamdb_database > /tmp/streamdb_backup.sql
```

**What to expect:**
- Database dump file to be created
- File size to reflect your database size
- No error messages during export

**If issues found:**
- Check MySQL credentials
- Verify database names: `mysql -u root -p -e "SHOW DATABASES;"`
- Ensure sufficient disk space in /tmp

### 8.2: Transfer Database to New Server
**What to do:** Copy and import database to new backend server
**When to do:** After creating backup
**Where to go:** Transfer between servers

```bash
# From your local computer or old server
scp root@***********:/tmp/full_database_backup.sql /tmp/

# Copy to new backend server
scp /tmp/full_database_backup.sql root@[NEW-BACKEND-IP]:/tmp/
```

**Import on new server:**
```bash
# SSH to new backend server
ssh root@[NEW-BACKEND-IP]

# Import database
mysql -u root -p < /tmp/full_database_backup.sql
```

**What to expect:**
- Database import to complete without errors
- All tables and data to be present
- Website database to be accessible

**If issues found:**
- Check MySQL error logs during import
- Verify database user permissions
- Ensure MySQL version compatibility

### 8.3: Transfer Website Files
**What to do:** Copy all website files to new server
**When to do:** After database transfer
**Where to go:** Transfer from old to new backend server

**First, identify website location from your analysis:**
```bash
# On old server, check your documented website location
# Common locations: /var/www/, /home/<USER>/public_html/
```

**Create directory structure on new server:**
```bash
# SSH to new backend server
ssh root@[NEW-BACKEND-IP]

# Create website directory (adjust path based on your analysis)
mkdir -p /var/www/streamdb.online
```

**Transfer files:**
```bash
# From old server to new server (adjust paths based on your analysis)
rsync -avz root@***********:/var/www/streamdb.online/ root@[NEW-BACKEND-IP]:/var/www/streamdb.online/
```

**Set correct permissions:**
```bash
# On new backend server
chown -R www-data:www-data /var/www/streamdb.online
chmod -R 755 /var/www/streamdb.online
```

**What to expect:**
- All website files to be transferred
- Correct permissions to be set
- File structure to match original

**If issues found:**
- Check disk space on new server
- Verify file paths are correct
- Ensure network connectivity between servers

### 8.4: Update Database Connection Settings
**What to do:** Update any database connection configurations
**When to do:** After file transfer
**Where to go:** Website configuration files

**Common files to check:**
```bash
# Look for database configuration files
find /var/www/streamdb.online -name "*.php" -exec grep -l "mysql\|database" {} \;
find /var/www/streamdb.online -name "config.php" -o -name "wp-config.php" -o -name ".env"
```

**Update database settings:**
- Database host: usually 'localhost' (should remain the same)
- Database name: verify it matches imported database
- Database user/password: update if changed

**What to expect:**
- Database connections to work
- Website to connect to database successfully
- No database connection errors

**If issues found:**
- Check database user permissions
- Verify database name exists
- Test database connection manually

---

# PHASE 7: CONFIGURE SSL CERTIFICATES

## Step 9: Setup SSL Certificates

**What to do:** Configure SSL certificates on reverse proxy
**When to do:** After website is working on HTTP
**Where to go:** New reverse proxy server

### 9.1: Install Certbot (for Let's Encrypt)
**What to do:** Install Let's Encrypt client
**When to do:** If your analysis shows Let's Encrypt certificates
**Where to go:** SSH to new reverse proxy server

```bash
# Install Certbot
apt install -y certbot python3-certbot-nginx

# Or for Apache backend (if needed)
apt install -y python3-certbot-apache
```

**What to expect:**
- Certbot to install successfully
- NGINX plugin to be available

**If issues found:**
- Check package repositories
- Install snapd version if apt version fails

### 9.2: Obtain SSL Certificates
**What to do:** Get SSL certificates for your domain
**When to do:** After Certbot installation
**Where to go:** Run certbot command

**Before running certbot, ensure:**
1. DNS points to new reverse proxy server (temporarily for testing)
2. Domain is accessible on port 80
3. NGINX is running

```bash
# Test domain accessibility first
curl -I http://streamdb.online

# Obtain certificate
certbot --nginx -d streamdb.online -d www.streamdb.online
```

**What to expect:**
- Certificate to be obtained successfully
- NGINX configuration to be updated automatically
- HTTPS to work immediately

**If issues found:**
- Ensure domain points to new server
- Check firewall allows port 80
- Verify NGINX is serving the domain

### 9.3: Update NGINX Configuration for SSL
**What to do:** Verify and optimize SSL configuration
**When to do:** After certificate installation
**Where to go:** Edit NGINX virtual host

```bash
# Check updated configuration
nano /etc/nginx/sites-available/streamdb.online
```

**Verify SSL configuration includes:**
```nginx
server {
    listen 443 ssl;
    server_name streamdb.online www.streamdb.online;
    
    ssl_certificate /etc/letsencrypt/live/streamdb.online/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/streamdb.online/privkey.pem;
    
    # SSL optimization
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    location / {
        proxy_pass http://[NEW-BACKEND-IP];
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name streamdb.online www.streamdb.online;
    return 301 https://$server_name$request_uri;
}
```

**Test and reload:**
```bash
nginx -t
systemctl reload nginx
```

**What to expect:**
- HTTPS to work correctly
- HTTP to redirect to HTTPS
- SSL certificate to be valid

**If issues found:**
- Check certificate paths
- Verify SSL configuration syntax
- Test with SSL checker tools

---

# PHASE 8: DNS MIGRATION

## Step 10: Migrate DNS to New Servers

**What to do:** Update DNS records to point to new servers
**When to do:** After all services are working and tested
**Where to go:** Cloudflare dashboard

### 10.1: Pre-Migration DNS Preparation
**What to do:** Prepare for DNS migration
**When to do:** 24-48 hours before migration
**Where to go:** Cloudflare dashboard

**Lower TTL values:**
1. Login to Cloudflare dashboard
2. Go to DNS section
3. Edit each A record
4. Change TTL to 300 seconds (5 minutes)
5. Save changes

**What to expect:**
- TTL changes to take effect within current TTL period
- Faster DNS propagation during migration

**If issues found:**
- Some records may have minimum TTL limits
- Changes may take time to propagate

### 10.2: Test New Setup Before DNS Change
**What to do:** Verify everything works with new servers
**When to do:** Before changing DNS
**Where to go:** Test using hosts file or direct IP

**Test using hosts file:**
```bash
# On your local computer, edit hosts file
# Windows: C:\Windows\System32\drivers\etc\hosts
# Linux/Mac: /etc/hosts

# Add these lines temporarily
[NEW-REVERSE-PROXY-IP] streamdb.online
[NEW-REVERSE-PROXY-IP] www.streamdb.online
```

**Test website functionality:**
1. Browse to https://streamdb.online
2. Test all major website functions
3. Check database connectivity
4. Verify SSL certificate
5. Test from multiple browsers

**What to expect:**
- Website to load correctly
- All functionality to work
- SSL certificate to be valid
- Performance to be acceptable

**If issues found:**
- Fix issues before DNS migration
- Test thoroughly until everything works
- Document any remaining issues

### 10.3: Execute DNS Migration
**What to do:** Update DNS records to new server
**When to do:** After thorough testing
**Where to go:** Cloudflare dashboard

**Update A records:**
1. Login to Cloudflare dashboard
2. Go to DNS section
3. Edit A record for @ (root domain)
4. Change IP from ************* to [NEW-REVERSE-PROXY-IP]
5. Save changes
6. Edit A record for www
7. Change IP to [NEW-REVERSE-PROXY-IP]
8. Save changes

**What to expect:**
- DNS changes to propagate within 5 minutes (due to low TTL)
- Traffic to start flowing to new servers
- Website to remain accessible

**If issues found:**
- Revert DNS changes immediately
- Check new server status
- Verify configurations are correct

### 10.4: Monitor Migration
**What to do:** Monitor traffic and performance
**When to do:** Immediately after DNS change
**Where to go:** Monitor both old and new servers

**Monitor new servers:**
```bash
# Check NGINX access logs
tail -f /var/log/nginx/access.log

# Check system resources
htop

# Check for errors
tail -f /var/log/nginx/error.log
```

**Monitor old servers:**
```bash
# Check if traffic is decreasing
tail -f /var/log/nginx/access.log
```

**What to expect:**
- Traffic to gradually shift to new servers
- Old server traffic to decrease
- New servers to handle load properly

**If issues found:**
- High error rates: investigate immediately
- Performance issues: check server resources
- Complete failure: revert DNS immediately

---

# PHASE 9: POST-MIGRATION VERIFICATION

## Step 11: Comprehensive Testing and Verification

**What to do:** Thoroughly test all functionality
**When to do:** After DNS migration is complete
**Where to go:** Test from multiple locations and devices

### 11.1: Functionality Testing
**What to do:** Test all website features
**When to do:** Within first hour of DNS migration

**Test checklist:**
- [ ] Homepage loads correctly
- [ ] All navigation links work
- [ ] User registration/login works
- [ ] Database searches work
- [ ] File uploads work (if applicable)
- [ ] Contact forms work (if applicable)
- [ ] Admin panel access works
- [ ] All images and media load
- [ ] Mobile responsiveness works

**What to expect:**
- All features to work as before
- No broken links or missing content
- Database operations to function correctly

**If issues found:**
- Check server error logs
- Verify database connections
- Test individual components

### 11.2: Performance Testing
**What to do:** Verify performance meets expectations
**When to do:** After functionality testing

**Performance checks:**
```bash
# Test page load times
curl -w "@curl-format.txt" -o /dev/null -s https://streamdb.online

# Check server response times
ping [NEW-REVERSE-PROXY-IP]

# Monitor server resources
htop
```

**Use online tools:**
- GTmetrix: https://gtmetrix.com/
- PageSpeed Insights: https://pagespeed.web.dev/
- Pingdom: https://tools.pingdom.com/

**What to expect:**
- Page load times similar to or better than before
- Server response times under 200ms
- Good performance scores

**If issues found:**
- Check server resources (CPU, RAM, disk)
- Optimize configurations if needed
- Consider server upgrades if performance is poor

### 11.3: Security Testing
**What to do:** Verify security configurations
**When to do:** After performance testing

**Security checks:**
- [ ] SSL certificate is valid and trusted
- [ ] HTTPS redirects work correctly
- [ ] Security headers are present
- [ ] Firewall rules are working
- [ ] Only necessary ports are open
- [ ] Backend server is not directly accessible

**Test SSL:**
```bash
# Test SSL configuration
openssl s_client -connect streamdb.online:443

# Check certificate details
curl -vI https://streamdb.online
```

**Use online tools:**
- SSL Labs: https://www.ssllabs.com/ssltest/
- Security Headers: https://securityheaders.com/

**What to expect:**
- A+ SSL rating
- All security headers present
- No security warnings

**If issues found:**
- Update SSL configuration
- Add missing security headers
- Review firewall rules

### 11.4: Backup Verification
**What to do:** Ensure backup systems are working
**When to do:** After security testing

**Verify backups:**
```bash
# Check if backup scripts are running
crontab -l

# Test database backup
mysqldump -u root -p --all-databases > /tmp/test_backup.sql

# Test file backup
tar -czf /tmp/website_backup.tar.gz /var/www/streamdb.online
```

**What to expect:**
- Backup scripts to be configured
- Backups to complete successfully
- Backup files to be created

**If issues found:**
- Configure backup scripts
- Test backup restoration
- Set up monitoring for backup failures

---

# PHASE 10: CLEANUP AND OPTIMIZATION

## Step 12: Final Cleanup and Optimization

**What to do:** Clean up and optimize the new setup
**When to do:** 24-48 hours after successful migration
**Where to go:** Both new servers

### 12.1: Increase DNS TTL
**What to do:** Restore normal DNS TTL values
**When to do:** After 24 hours of stable operation
**Where to go:** Cloudflare dashboard

**Update TTL values:**
1. Login to Cloudflare dashboard
2. Go to DNS section
3. Edit each A record
4. Change TTL back to 3600 seconds (1 hour) or Auto
5. Save changes

**What to expect:**
- DNS changes to be cached longer
- Reduced DNS queries
- More stable DNS resolution

### 12.2: Update Documentation
**What to do:** Document new server details
**When to do:** After successful migration

**Update these details:**
- New server IP addresses
- New server credentials
- Updated configurations
- Lessons learned during migration
- Any issues encountered and solutions

**Create new documentation:**
- New server specifications
- Updated network diagram
- New backup procedures
- Updated monitoring configurations

### 12.3: Monitor and Optimize
**What to do:** Set up ongoing monitoring
**When to do:** After migration is complete

**Set up monitoring for:**
- Server uptime and performance
- Website availability
- SSL certificate expiration
- Backup completion
- Security alerts

**Optimization tasks:**
- Review server resource usage
- Optimize database queries if needed
- Configure caching if beneficial
- Update security configurations

### 12.4: Plan Old Server Shutdown
**What to do:** Safely decommission old servers
**When to do:** 1 week after successful migration

**Before shutting down old servers:**
- [ ] Verify all data has been migrated
- [ ] Confirm backups are working on new servers
- [ ] Test disaster recovery procedures
- [ ] Update any hardcoded IP references
- [ ] Cancel old server subscriptions

**Shutdown procedure:**
1. Stop services on old servers
2. Keep servers for 1 more week (powered off)
3. Verify no issues arise
4. Permanently delete old servers
5. Update inventory and documentation

---

# EMERGENCY PROCEDURES

## Rollback Procedures

### If Migration Fails Completely
**Immediate actions:**
1. **Revert DNS immediately:**
   - Change A records back to *************
   - Wait for propagation (5 minutes with low TTL)

2. **Verify old servers:**
   - Check old servers are still running
   - Test website functionality
   - Monitor for any issues

3. **Investigate new servers:**
   - Check error logs
   - Verify configurations
   - Test connectivity

### If Partial Functionality Lost
**Assessment actions:**
1. **Identify affected features:**
   - Test each major function
   - Check error logs for patterns
   - Determine scope of issues

2. **Quick fixes:**
   - Check database connectivity
   - Verify file permissions
   - Test SSL certificates
   - Review proxy configurations

3. **Decision point:**
   - If fixable quickly (< 30 minutes): fix and continue
   - If complex issues: consider rollback

### Emergency Contacts
**Prepare these contacts before migration:**
- Hosting provider support: [PHONE/EMAIL]
- Domain registrar support: [PHONE/EMAIL]
- Cloudflare support: [PHONE/EMAIL]
- Technical team lead: [PHONE/EMAIL]
- Emergency contact: [PHONE/EMAIL]

---

# SUCCESS CRITERIA CHECKLIST

## Migration is Successful When:
- [ ] Website loads correctly from multiple locations
- [ ] All functionality works as before migration
- [ ] SSL certificates are valid and working
- [ ] Performance is equal to or better than before
- [ ] No critical errors in server logs
- [ ] Database operations work correctly
- [ ] File uploads/downloads work correctly
- [ ] Email functionality works (if applicable)
- [ ] Backup systems are operational
- [ ] Monitoring systems are updated and working
- [ ] Security configurations are properly applied
- [ ] DNS propagation is complete globally

## Final Verification Steps:
1. **Test from multiple locations worldwide**
2. **Verify with different devices and browsers**
3. **Check all critical business functions**
4. **Monitor for 48 hours continuously**
5. **Verify backup and recovery procedures**
6. **Update all documentation and procedures**

---

**Congratulations!** If you've completed all these steps successfully, you have migrated your reverse proxy setup to new servers while maintaining the exact same configuration and functionality. Your new setup should be more reliable, secure, and ready for future growth.
