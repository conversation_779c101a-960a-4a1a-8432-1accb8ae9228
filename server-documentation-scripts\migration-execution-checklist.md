# Migration Execution Checklist

## Pre-Migration Phase (1-2 Days Before)

### Day -2: Final Preparation
- [ ] **Review all documentation** generated by audit scripts
- [ ] **Complete Cloudflare documentation** using the template
- [ ] **Order new servers** with specifications matching current setup
- [ ] **Prepare backup strategy** for current servers
- [ ] **Set up monitoring** for current servers to track performance during migration
- [ ] **Notify stakeholders** about planned migration window
- [ ] **Prepare rollback plan** with specific steps and contacts

### Day -1: Pre-Migration Setup
- [ ] **Lower DNS TTL** to 300 seconds (5 minutes) for all records
- [ ] **Create full backup** of current website files and database
- [ ] **Test backup restoration** on a test environment
- [ ] **Prepare SSL certificates** for transfer to new servers
- [ ] **Set up new servers** with base OS and network configuration
- [ ] **Install required packages** on new servers (don't configure yet)
- [ ] **Verify network connectivity** between new servers
- [ ] **Test SSH access** to both new servers

## Migration Day: Hour-by-Hour Execution

### Hour 0: Migration Start
- [ ] **Announce maintenance window** to users
- [ ] **Enable maintenance mode** on current website (optional)
- [ ] **Take final backup** of database and files
- [ ] **Document current server status** (uptime, performance metrics)
- [ ] **Begin configuration of new reverse proxy server**

### Hour 1: Reverse Proxy Configuration
- [ ] **Install NGINX** with same version and modules as current
- [ ] **Copy NGINX configuration files** from documentation
- [ ] **Install SSL certificates** on new reverse proxy
- [ ] **Configure firewall rules** matching current setup
- [ ] **Test NGINX configuration** syntax: `nginx -t`
- [ ] **Start NGINX service** and verify it's running
- [ ] **Test internal connectivity** to backend server

### Hour 2: Backend Server Configuration
- [ ] **Install FastPanel** (if used) with same configuration
- [ ] **Install and configure MySQL** with same settings
- [ ] **Install PHP** with same version and extensions
- [ ] **Install web server** (Apache/NGINX) with same configuration
- [ ] **Configure firewall** to allow only reverse proxy access
- [ ] **Test all services** are running correctly

### Hour 3: Data Migration
- [ ] **Transfer website files** to new backend server
- [ ] **Set correct file permissions** and ownership
- [ ] **Import database** to new MySQL server
- [ ] **Update database connection settings** if needed
- [ ] **Test database connectivity** from web application
- [ ] **Verify all website files** are accessible

### Hour 4: Configuration Verification
- [ ] **Test reverse proxy** → backend connectivity
- [ ] **Verify SSL certificates** are working correctly
- [ ] **Test website functionality** through new servers
- [ ] **Check all website pages** load correctly
- [ ] **Test user authentication** and database operations
- [ ] **Verify file uploads/downloads** work correctly
- [ ] **Check email functionality** if applicable

### Hour 5: DNS Migration
- [ ] **Update A record** for @ (root domain) to new reverse proxy IP
- [ ] **Update A record** for www to new reverse proxy IP
- [ ] **Update any other A records** pointing to old servers
- [ ] **Monitor DNS propagation** using online tools
- [ ] **Test website access** from multiple locations
- [ ] **Monitor server logs** for incoming traffic

### Hour 6: Post-Migration Verification
- [ ] **Verify all website functionality** is working
- [ ] **Check SSL certificate** validity and configuration
- [ ] **Test from multiple devices** and browsers
- [ ] **Monitor server performance** and resource usage
- [ ] **Check error logs** for any issues
- [ ] **Verify backup systems** are working on new servers

## Post-Migration Phase (Next 24-48 Hours)

### First 6 Hours After DNS Change
- [ ] **Monitor website uptime** continuously
- [ ] **Check server resource usage** (CPU, memory, disk)
- [ ] **Monitor error logs** for any issues
- [ ] **Test all critical functionality** every hour
- [ ] **Respond to any user reports** of issues
- [ ] **Keep old servers running** as backup

### Day +1: Stability Verification
- [ ] **Verify 24-hour uptime** on new servers
- [ ] **Check performance metrics** compared to old servers
- [ ] **Review all error logs** for patterns
- [ ] **Test all website features** thoroughly
- [ ] **Verify backup systems** completed successfully
- [ ] **Update monitoring systems** to point to new servers

### Day +2: Final Cleanup
- [ ] **Increase DNS TTL** back to normal (3600 seconds)
- [ ] **Update documentation** with new server details
- [ ] **Schedule old server shutdown** (keep for 1 week minimum)
- [ ] **Update any hardcoded IP addresses** in configurations
- [ ] **Notify stakeholders** of successful migration
- [ ] **Document lessons learned** for future migrations

## Emergency Procedures

### If Migration Fails (Rollback Required)
1. **Immediate Actions:**
   - [ ] Change DNS records back to old server IPs
   - [ ] Announce rollback to users
   - [ ] Monitor old servers for stability
   - [ ] Document failure reasons

2. **Investigation:**
   - [ ] Check new server logs for errors
   - [ ] Verify network connectivity issues
   - [ ] Test configurations on new servers
   - [ ] Contact hosting provider if needed

3. **Recovery Planning:**
   - [ ] Fix identified issues on new servers
   - [ ] Plan new migration window
   - [ ] Update migration procedures based on lessons learned

### If Partial Functionality Lost
1. **Immediate Assessment:**
   - [ ] Identify which features are not working
   - [ ] Check if it's DNS, server, or application issue
   - [ ] Determine if rollback is necessary

2. **Quick Fixes:**
   - [ ] Check NGINX configuration for errors
   - [ ] Verify database connectivity
   - [ ] Check file permissions
   - [ ] Review SSL certificate configuration

3. **Communication:**
   - [ ] Inform users of specific issues
   - [ ] Provide estimated resolution time
   - [ ] Update status page if available

## Critical Contact Information

### Hosting Providers
- **Current Reverse Proxy Provider:** [FILL IN CONTACT INFO]
- **Current Backend Provider:** [FILL IN CONTACT INFO]
- **New Hosting Provider:** [FILL IN CONTACT INFO]

### Domain and DNS
- **Domain Registrar:** [FILL IN CONTACT INFO]
- **Cloudflare Support:** [FILL IN CONTACT INFO]

### Technical Support
- **Primary Technical Contact:** [FILL IN]
- **Secondary Technical Contact:** [FILL IN]
- **Emergency Contact:** [FILL IN]

## Success Criteria

### Migration is Successful When:
- [ ] Website loads correctly from multiple locations
- [ ] All functionality works as before migration
- [ ] SSL certificates are valid and working
- [ ] Performance is equal to or better than before
- [ ] No critical errors in server logs
- [ ] Database operations work correctly
- [ ] File uploads/downloads work correctly
- [ ] Email functionality works (if applicable)
- [ ] Backup systems are operational
- [ ] Monitoring systems are updated and working

### Performance Benchmarks
- **Page Load Time:** Should be ≤ [FILL IN CURRENT BASELINE]
- **Database Query Time:** Should be ≤ [FILL IN CURRENT BASELINE]
- **Server Response Time:** Should be ≤ [FILL IN CURRENT BASELINE]
- **Uptime:** Should maintain 99.9%+ uptime

## Tools and Commands for Migration

### Useful Commands During Migration
```bash
# Test NGINX configuration
nginx -t

# Reload NGINX without downtime
nginx -s reload

# Check service status
systemctl status nginx
systemctl status mysql
systemctl status apache2

# Monitor real-time logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# Test database connectivity
mysql -u username -p -h localhost

# Check disk space
df -h

# Check memory usage
free -h

# Check network connectivity
ping [target-server]
telnet [target-server] [port]

# DNS propagation check
nslookup streamdb.online
dig streamdb.online
```

### Online Tools for Verification
- **DNS Propagation:** https://dnschecker.org/
- **SSL Certificate Check:** https://www.ssllabs.com/ssltest/
- **Website Speed Test:** https://gtmetrix.com/
- **Uptime Monitoring:** https://uptimerobot.com/

## Final Notes

- **Keep detailed logs** of all actions taken during migration
- **Take screenshots** of important configurations
- **Document any deviations** from the planned procedure
- **Save all error messages** for troubleshooting
- **Keep old servers running** for at least 1 week after successful migration
- **Update all documentation** with new server information after migration
